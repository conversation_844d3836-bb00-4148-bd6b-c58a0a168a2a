import 'package:flutter/material.dart';
import 'lib/screen/ai_chat/ChatWithGPTPage.dart';
import 'lib/screen/ai_chat/enhanced_voice_chat.dart';

void main() {
  runApp(const TestAIChatApp());
}

class TestAIChatApp extends StatelessWidget {
  const TestAIChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار المرشد الذكي',
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'IBMPlexSansArabic',
      ),
      home: const TestHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class TestHomePage extends StatelessWidget {
  const TestHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار المرشد الذكي'),
        backgroundColor: const Color(0xFF4F908E),
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF4F908E).withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.smart_toy,
                size: 100,
                color: Color(0xFF4F908E),
              ),
              const SizedBox(height: 30),
              const Text(
                'اختبار المرشد الذكي المحسن',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              const Text(
                'اختر نوع المحادثة التي تريد تجربتها',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 50),
              
              // زر المحادثة النصية
              Container(
                width: 280,
                height: 60,
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ChatWithGPTPage(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.chat, size: 28),
                  label: const Text(
                    'المحادثة النصية المحسنة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4F908E),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    elevation: 5,
                  ),
                ),
              ),
              
              // زر المحادثة الصوتية
              Container(
                width: 280,
                height: 60,
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const EnhancedVoiceChatScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.mic, size: 28),
                  label: const Text(
                    'المحادثة الصوتية المبهرة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2E8B57),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    elevation: 5,
                  ),
                ),
              ),
              
              const SizedBox(height: 40),
              
              // معلومات الميزات
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 30),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Column(
                  children: [
                    Text(
                      '✨ الميزات الجديدة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4F908E),
                      ),
                    ),
                    SizedBox(height: 15),
                    Text(
                      '• 8 أزرار سريعة مفيدة للزوار\n'
                      '• تصميم مبهر مع تأثيرات بصرية\n'
                      '• محادثة صوتية تفاعلية\n'
                      '• رسوم متحركة متقدمة\n'
                      '• واجهة محسنة وسهلة الاستخدام',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
