import 'package:flutter/material.dart';
// import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../services/webrtc_service.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';
import 'dart:math' as math;

class ChatScreen extends StatefulWidget {
  const ChatScreen({Key? key}) : super(key: key);

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  final WebRTCService _webRTCService = WebRTCService();
  // final RTCVideoRenderer _remoteAudioRenderer = RTCVideoRenderer();
  bool _isConnected = false;
  String _status = 'جاهز للمحادثة';
  bool _isSpeaking = false;

  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  late AnimationController _pulseController;
  late List<AnimationController> _waveControllers;
  late List<Animation<double>> _waveAnimations;

  final int _numberOfWaves = 5;

  @override
  void initState() {
    super.initState();
    _initializeRenderers();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Scale animation for the logo
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeOutBack),
    );
    _scaleController.forward();

    // Pulse animation
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseController.repeat(reverse: true);

    // Wave animations
    _waveControllers = List.generate(
      _numberOfWaves,
      (index) => AnimationController(
        duration: Duration(seconds: 2 + index),
        vsync: this,
      ),
    );

    _waveAnimations = _waveControllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(parent: controller, curve: Curves.easeOutQuad));
    }).toList();

    for (var i = 0; i < _numberOfWaves; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _waveControllers[i].repeat();
        }
      });
    }
  }

  Future<void> _initializeRenderers() async {
    // await _remoteAudioRenderer.initialize();
  }

  Future<void> _startChat() async {
    setState(() => _status = 'جاري طلب الإذن للميكروفون...');

    final micPermission = await Permission.microphone.request();
    if (micPermission.isDenied) {
      setState(() => _status = 'تم رفض إذن الميكروفون');
      return;
    }

    try {
      setState(() => _status = 'جاري الإعداد...');
      await _webRTCService.initialize();

      // تفعيل السماعة الخارجية
      final Map<String, dynamic> mediaConstraints = {
        'audio': true,
        'video': false,
      };

      // final stream = await navigator.mediaDevices.getUserMedia(
      //   mediaConstraints,
      // );
      // stream.getAudioTracks()[0].enableSpeakerphone(
      //       true,
      //     ); // تفعيل السماعة الخارجية

      _webRTCService.onRemoteStream = (stream) {
        setState(() {
          // _remoteAudioRenderer.srcObject = stream;
          _isSpeaking = true;
        });
      };

      setState(() => _status = 'جاري الاتصال بالمساعد...');
      await _webRTCService.connect();

      setState(() {
        _isConnected = true;
        _status = 'متصل - يمكنك التحدث الآن';
      });
    } catch (e) {
      setState(() => _status = 'حدث خطأ: $e');
      print('Error: $e');
    }
  }

  @override
  void dispose() {
    _webRTCService.dispose();
    // _remoteAudioRenderer.dispose();
    _scaleController.dispose();
    _pulseController.dispose();
    for (var controller in _waveControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xFF4F908E).withOpacity(0.1),
                  Colors.white,
                ],
                stops: const [0.0, 0.3],
              ),
            ),
          ),

          // Main content
          SafeArea(
            child: Column(
              children: [
                // Custom AppBar
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.close, color: Color(0xFF4F908E)),
                        onPressed: () => Navigator.pop(context),
                      ),
                      Text(
                        'المحادثة الصوتية',
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF4F908E),
                        ),
                      ),
                      const SizedBox(width: 40), // Balance the close button
                    ],
                  ),
                ),

                // Main content area
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Logo and waves
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            if (_isSpeaking) ..._buildWaveAnimations(),
                            Container(
                              width: 300,
                              height: 300,
                              child: Lottie.asset(
                                'assets/animations/woman.json',
                                fit: BoxFit.contain,
                                animate: _isSpeaking,
                                repeat: true,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 40),

                        // Status text with animation
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 300),
                          child: Text(
                            _status,
                            key: ValueKey<String>(_status),
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF4F908E),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),

                        const SizedBox(height: 30),

                        // Start chat button with animation
                        if (!_isConnected)
                          ScaleTransition(
                            scale: _scaleAnimation,
                            child: ElevatedButton(
                              onPressed: _startChat,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF4F908E),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 32,
                                  vertical: 16,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                elevation: 5,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(Icons.mic, color: Colors.white),
                                  const SizedBox(width: 8),
                                  Text(
                                    'بدء المحادثة',
                                    style: GoogleFonts.ibmPlexSansArabic(
                                      fontSize: 18,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildWaveAnimations() {
    return List.generate(_numberOfWaves, (index) {
      return AnimatedBuilder(
        animation: _waveAnimations[index],
        builder: (context, child) {
          final scale = 1.0 + (_waveAnimations[index].value * 1.0);
          final opacity = (1.0 - _waveAnimations[index].value) * 0.7;
          return Positioned.fill(
            child: Center(
              child: Container(
                width: 300 * scale, // تحديث حجم الموجات لتتناسب مع الحجم الجديد
                height:
                    300 * scale, // تحديث حجم الموجات لتتناسب مع الحجم الجديد
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: const Color(0xFF4F908E).withOpacity(opacity),
                    width: 2,
                  ),
                ),
              ),
            ),
          );
        },
      );
    });
  }
}
