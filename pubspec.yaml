name: wiffada
description: A new Flutter project.
publish_to: 'none'
version: 0.1.0

environment:
  sdk: '>=3.0.6 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  google_fonts: ^4.0.4
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0
  share_plus: ^7.2.1
  http: ^1.1.0
  flutter_dotenv: ^5.1.0
  permission_handler: ^11.0.1
  flutter_localizations:
    sdk: flutter
  iconsax: ^0.0.8
  provider: ^6.0.5
  url_launcher: ^6.1.14
  dio: ^5.3.2
  adhan: ^2.0.0-nullsafety.2
  intl: 0.20.2
  google_maps_flutter: ^2.4.0
  flutter_staggered_animations: ^1.1.1
  glass_kit: ^3.0.0
  webview_flutter: ^4.4.2
  salomon_bottom_bar: ^3.3.2
  iconsax_plus: ^1.0.0
  chat_bubbles: ^1.5.0
  flutter_svg: ^2.0.9
  animated_text_kit: ^4.2.2
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  audio_session: ^0.1.18
  crypto: ^3.0.3
  geolocator: ^9.0.2
  flutter_map_marker_cluster: ^1.3.4
  cached_network_image: ^3.3.0
  flutter_animate: ^4.2.0
  flutter_tts: ^4.2.2
  lottie: ^2.7.0
  volume_controller: ^2.0.7
  video_player: ^2.6.1
  chewie: ^1.5.0
  just_audio: ^0.9.34
  flutter_html: ^3.0.0-beta.2
  photo_view: ^0.15.0
  youtube_player_flutter: ^9.0.0
  flutter_webrtc: ^0.9.48
  web_socket_channel: ^2.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.1
  flutter_lints: ^4.0.0

flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  adaptive_icon_background: "#4F908E"
  adaptive_icon_foreground: "assets/images/logo.png"
  remove_alpha_ios: true

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
    - assets/images/places/
    - assets/images/backgrounds/
    - assets/map_style.json
    - assets/animations/woman.json
    - assets/animations/AI.json
    - .env
    - assets/destinations.json
    - assets/data/mosque_data.json
