import 'package:flutter/material.dart';
import 'package:wiffada/services/chat_service.dart';
import 'chat_screen.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/cupertino.dart';
import 'DuaDetailsPage.dart';
import '../../services/content_service.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_localizations.dart';
import 'dart:math' as math;
import 'enhanced_voice_chat.dart';

// Quick Action Model
class QuickAction {
  final String title;
  final IconData icon;
  final Color color;
  final String query;

  QuickAction({
    required this.title,
    required this.icon,
    required this.color,
    required this.query,
  });
}

// Enhanced Chat Message Model
class ChatMessage {
  final String text;
  final bool isUserMessage;
  final DateTime timestamp;
  final MessageType type;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    required this.text,
    required this.isUserMessage,
    DateTime? timestamp,
    this.type = MessageType.text,
    this.metadata,
  }) : timestamp = timestamp ?? DateTime.now();
}

enum MessageType {
  text,
  image,
  location,
  audio,
  quickReply,
}

class ChatWithGPTPage extends StatefulWidget {
  const ChatWithGPTPage({super.key});

  @override
  State<ChatWithGPTPage> createState() => _ChatWithGPTPageState();
}

class _ChatWithGPTPageState extends State<ChatWithGPTPage>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  // تم استبدال ApiService بـ ChatService الجديد
  final ContentService _contentService = ContentService();
  bool _isTyping = false;
  final bool _isListening = false;
  final bool _showQuickActions = true;
  late AnimationController _fabController;
  late AnimationController _pulseController;
  late AnimationController _waveController;
  final FlutterTts flutterTts = FlutterTts();

  // Quick action categories - ميزات مفيدة فعلاً لزوار المدينة المنورة
  final List<QuickAction> _quickActions = [
    QuickAction(
      title: 'آداب الزيارة',
      icon: Iconsax.book_1,
      color: const Color(0xFF4F908E),
      query: 'ما هي آداب زيارة المسجد النبوي والروضة الشريفة؟',
    ),
    QuickAction(
      title: 'أوقات الزحام',
      icon: Iconsax.people,
      color: const Color(0xFF2E8B57),
      query: 'ما هي أفضل الأوقات لزيارة المسجد النبوي لتجنب الزحام؟',
    ),
    QuickAction(
      title: 'الأدعية المستجابة',
      icon: Iconsax.heart,
      color: const Color(0xFF8B4513),
      query: 'ما هي الأدعية المستجابة في المسجد النبوي والأماكن المقدسة؟',
    ),
    QuickAction(
      title: 'مسارات الزيارة',
      icon: Iconsax.routing_2,
      color: const Color(0xFF6A5ACD),
      query: 'ما هو أفضل مسار لزيارة المعالم الإسلامية في المدينة المنورة؟',
    ),
    QuickAction(
      title: 'نصائح مهمة',
      icon: Iconsax.info_circle,
      color: const Color(0xFFFF6B35),
      query: 'ما هي أهم النصائح والإرشادات للزوار الجدد في المدينة المنورة؟',
    ),
  ];

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _fabController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fabController.forward();
    _pulseController.repeat(reverse: true);
    _waveController.repeat();

    _initTts();
    _addWelcomeMessage();
  }

  void _addWelcomeMessage() {
    _messages.add(
      ChatMessage(
        text: """🌟 مرحباً بك في المرشد الذكي لوفادة!

أنا مساعدك الشخصي لخدمة زوار المدينة المنورة 🕌

يمكنني مساعدتك في آداب الزيارة، أفضل الأوقات، الأدعية المستجابة، تخطيط المسارات، والخدمات المجانية.

استخدم الأزرار أعلى مربع الكتابة أو اكتب سؤالك مباشرة! 💬""",
        isUserMessage: false,
        type: MessageType.text,
      ),
    );
  }

  Future<void> _initTts() async {
    await flutterTts.setLanguage("ar");
    await flutterTts.setSpeechRate(0.5);
    await flutterTts.setVolume(1.0);
    await flutterTts.setPitch(1.0);
  }

  Future<void> _speak(String text) async {
    await flutterTts.stop();
    await flutterTts.speak(text);
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _fabController.dispose();
    _pulseController.dispose();
    _waveController.dispose();
    flutterTts.stop();
    super.dispose();
  }

  // Handle quick action tap
  void _handleQuickAction(QuickAction action) {
    _handleSubmitted(action.query);
  }

  // Start voice chat
  void _startVoiceChat() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EnhancedVoiceChatScreen(),
      ),
    );
  }

  // Enhanced AppBar with animations
  PreferredSizeWidget _buildEnhancedAppBar(bool isSmallScreen) {
    return AppBar(
      toolbarHeight: 80,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF4F908E),
              Color(0xFF6BA3A0),
              Color(0xFF4F908E),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.15),
              blurRadius: 15,
              spreadRadius: 0,
              offset: const Offset(0, 5),
            ),
          ],
          borderRadius: const BorderRadius.vertical(
            bottom: Radius.circular(30),
          ),
        ),
      ),
      leading: IconButton(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Iconsax.arrow_left_2,
            color: Colors.white,
            size: 20,
          ),
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + (_pulseController.value * 0.1),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.3),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Iconsax.message_programming,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'المرشد الذكي',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'مساعدك الشخصي في المدينة المنورة',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Iconsax.setting_2,
              color: Colors.white,
              size: 20,
            ),
          ),
          onPressed: () {
            // Settings functionality
          },
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  // Build Compact Quick Actions - أزرار مصغرة فوق الكتابة
  Widget _buildCompactQuickActions() {
    return Container(
      height: 50,
      margin: const EdgeInsets.only(bottom: 12),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        itemCount: _quickActions.length,
        itemBuilder: (context, index) {
          final action = _quickActions[index];
          return _buildCompactActionChip(action, index);
        },
      ),
    );
  }

  Widget _buildCompactActionChip(QuickAction action, int index) {
    return AnimatedBuilder(
      animation: _waveController,
      builder: (context, child) {
        final delay = index * 0.1;
        final animationValue = (_waveController.value + delay) % 1.0;

        return Container(
          margin: const EdgeInsets.only(right: 8),
          child: Transform.scale(
            scale: 1.0 + (math.sin(animationValue * 2 * math.pi) * 0.01),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(25),
                onTap: () => _handleQuickAction(action),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        action.color.withValues(alpha: 0.15),
                        action.color.withValues(alpha: 0.08),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: action.color.withValues(alpha: 0.3),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: action.color.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        action.icon,
                        color: action.color,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        action.title,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: action.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleSubmitted(String text) async {
    if (text.trim().isEmpty) return;

    setState(() {
      _messages.add(ChatMessage(
        text: text,
        isUserMessage: true,
      ));
      _isTyping = true;
    });

    _messageController.clear();
    _scrollController.animateTo(
      0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );

    String? contentType = _getContentType(text);
    if (contentType != null) {
      setState(() {
        _messages.add(ChatMessage(
          text: "أبشر ✨\nسوف يتم نقلك الآن إلى صفحة تفصيلية...",
          isUserMessage: false,
        ));
        _isTyping = false;
      });

      await Future.delayed(const Duration(seconds: 1));
      _navigateToDetailsPage(contentType);
      return;
    }

    try {
      final response = await ChatService.sendTextMessage(text);
      setState(() {
        _messages.add(ChatMessage(text: response, isUserMessage: false));
        _isTyping = false;
      });

      _scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    } catch (e) {
      setState(() {
        _messages.add(ChatMessage(
          text: 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.',
          isUserMessage: false,
        ));
        _isTyping = false;
      });
    }
  }

  String? _getContentType(String text) {
    final contentTypes = {
      'duas': ['الأدعية المأثورة', 'الأدعية', 'دعاء'],
      'adab': ['آداب زيارة المسجد النبوي', 'آداب الزيارة'],
      'rawdah': ['الروضة الشريفة', 'الروضة'],
      'fadl': ['فضل المدينة', 'فضائل المدينة'],
    };

    for (var entry in contentTypes.entries) {
      if (entry.value.any((keyword) => text.contains(keyword))) {
        return entry.key;
      }
    }
    return null;
  }

  void _navigateToDetailsPage(String contentType) async {
    final content = await _contentService.getContentByType(contentType);
    if (content != null) {
      Navigator.push(
        // ignore: use_build_context_synchronously
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              DuaDetailsPage(
            content: content,
          ),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  /// Initiates a navigation to the ChatScreen with a slide transition.
  ///
  /// This method pushes the ChatScreen onto the navigation stack using a
  /// [PageRouteBuilder]. The transition is a slide from the bottom to the top
  /// of the screen, implemented using a [SlideTransition] with a cubic ease-out
  /// curve. The transition duration is 500 milliseconds.

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth < 360;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildEnhancedAppBar(isSmallScreen),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF4F908E).withValues(alpha: 0.05),
              Colors.white.withValues(alpha: 0.8),
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Chat Messages
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(25),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: ListView.builder(
                    controller: _scrollController,
                    reverse: true,
                    padding: EdgeInsets.symmetric(
                      horizontal: screenWidth * 0.04,
                      vertical: screenHeight * 0.02,
                    ),
                    itemCount: _messages.length + (_isTyping ? 1 : 0),
                    itemBuilder: (context, index) {
                      final messageIndex = (_messages.length - 1) -
                          (index - (_isTyping ? 1 : 0));

                      if (_isTyping && index == 0) {
                        return Container(
                          padding: const EdgeInsets.all(16),
                          margin: const EdgeInsets.only(bottom: 8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CupertinoActivityIndicator(),
                              SizedBox(width: 8),
                              Text(
                                'جاري الكتابة...',
                                style: TextStyle(
                                  color: Color(0xFF4F908E),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      }

                      final message = _messages[messageIndex];
                      return Padding(
                        padding: EdgeInsets.only(
                          bottom: screenHeight * 0.015,
                          left: message.isUserMessage ? screenWidth * 0.15 : 0,
                          right: message.isUserMessage ? 0 : screenWidth * 0.15,
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: message.isUserMessage
                                  ? [
                                      const Color(0xFF4F908E),
                                      const Color(0xFF3D7A79),
                                    ]
                                  : [
                                      Colors.white,
                                      Colors.white.withValues(alpha: 0.9),
                                    ],
                            ),
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.04,
                            vertical: screenHeight * 0.015,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              if (!message.isUserMessage) // فقط لرسائل AI
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: IconButton(
                                    icon: const Icon(
                                      Icons.volume_up_rounded,
                                      color: Color(0xFF4F908E),
                                      size: 20,
                                    ),
                                    onPressed: () => _speak(message.text),
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(
                                      minWidth: 24,
                                      minHeight: 24,
                                    ),
                                  ),
                                ),
                              Text(
                                message.text,
                                style: GoogleFonts.ibmPlexSansArabic(
                                  color: message.isUserMessage
                                      ? Colors.white
                                      : Colors.black87,
                                  fontSize: 16,
                                ),
                                textDirection: TextDirection.rtl,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),

              // Enhanced Input Section
              _buildEnhancedInputSection(screenWidth, screenHeight),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedInputSection(double screenWidth, double screenHeight) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.04,
        vertical: screenHeight * 0.015,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 15,
            offset: const Offset(0, -5),
          ),
        ],
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(25),
        ),
      ),
      child: Column(
        children: [
          // Quick Actions Row - مصغرة وجميلة
          _buildCompactQuickActions(),

          // Typing indicator
          if (_isTyping) _buildTypingIndicator(),

          // Input row
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: const Color(0xFF4F908E).withValues(alpha: 0.2),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // Voice input button
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(25),
                          onTap: _startVoiceChat,
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            child: AnimatedBuilder(
                              animation: _pulseController,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: 1.0 + (_pulseController.value * 0.1),
                                  child: const Icon(
                                    Iconsax.microphone_2,
                                    color: Color(0xFF4F908E),
                                    size: 24,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                      // Text input
                      Expanded(
                        child: TextField(
                          controller: _messageController,
                          textDirection: TextDirection.rtl,
                          maxLines: null,
                          decoration: InputDecoration(
                            hintText: 'اكتب رسالتك هنا...',
                            hintStyle: GoogleFonts.ibmPlexSansArabic(
                              color: Colors.grey[400],
                            ),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                          ),
                          onSubmitted: _handleSubmitted,
                        ),
                      ),
                      // Attachment button
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(25),
                          onTap: () {
                            // Attachment functionality
                          },
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            child: const Icon(
                              Iconsax.attach_circle,
                              color: Color(0xFF4F908E),
                              size: 24,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Send button
              AnimatedBuilder(
                animation: _fabController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _fabController.value,
                    child: Material(
                      color: const Color(0xFF4F908E),
                      borderRadius: BorderRadius.circular(25),
                      elevation: 5,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(25),
                        onTap: () => _handleSubmitted(_messageController.text),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          child: const Icon(
                            Iconsax.send_1,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF4F908E).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CupertinoActivityIndicator(radius: 8),
          const SizedBox(width: 8),
          Text(
            'المرشد يكتب...',
            style: GoogleFonts.ibmPlexSansArabic(
              color: const Color(0xFF4F908E),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildSuggestionChip(String label, String question) {
  //   double screenWidth = MediaQuery.of(context).size.width;
  //   double maxWidth = screenWidth < 360 ? screenWidth * 0.35 : screenWidth * 0.4;

  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 4),
  //     child: GestureDetector(
  //       onTap: () {
  //         setState(() {
  //           _messageController.text = question;
  //           _handleSubmitted(question);
  //         });
  //       },
  //       child: Container(
  //         constraints: BoxConstraints(
  //           maxWidth: maxWidth,
  //           minWidth: 100,
  //         ),
  //         padding: EdgeInsets.symmetric(
  //           horizontal: screenWidth * 0.03,
  //           vertical: 8,
  //         ),
  //         decoration: BoxDecoration(
  //           gradient: LinearGradient(
  //             colors: [
  //               const Color(0xFF4F908E).withOpacity(0.15),
  //               const Color(0xFF4F908E).withOpacity(0.25),
  //             ],
  //             begin: Alignment.topLeft,
  //             end: Alignment.bottomRight,
  //           ),
  //           borderRadius: BorderRadius.circular(20),
  //           boxShadow: [
  //             BoxShadow(
  //               color: Colors.black.withOpacity(0.05),
  //               blurRadius: 4,
  //               offset: const Offset(0, 2),
  //             ),
  //           ],
  //           border: Border.all(
  //             color: const Color(0xFF4F908E).withOpacity(0.3),
  //             width: 1,
  //           ),
  //         ),
  //         child: Text(
  //           label,
  //           style: GoogleFonts.ibmPlexSansArabic(
  //             fontSize: screenWidth < 360 ? 11 : 13,
  //             color: const Color(0xFF4F908E),
  //             fontWeight: FontWeight.w600,
  //           ),
  //           textAlign: TextAlign.center,
  //           maxLines: 2,
  //           overflow: TextOverflow.ellipsis,
  //         ),
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildSuggestionsStrip() {
  //   double screenWidth = MediaQuery.of(context).size.width;
  //   double stripHeight = screenWidth < 360 ? 45 : 40;

  //   return Container(
  //     height: stripHeight,
  //     margin: EdgeInsets.symmetric(
  //       horizontal: screenWidth * 0.02,
  //       vertical: 4,
  //     ),
  //     child: ListView(
  //       scrollDirection: Axis.horizontal,
  //       physics: const BouncingScrollPhysics(),
  //       padding: EdgeInsets.symmetric(
  //         horizontal: screenWidth * 0.02,
  //       ),
  //       children: [
  //            _buildSuggestionChip(
  //           'ماهي خدمات الجمعية',
  //           'ماهي خدمات الجمعية؟',
  //         ),
  //         _buildSuggestionChip(
  //           'الأدعية المأثورة 📿',
  //           'ما هي الأدعية المأثورة في المسجد النبوي وأماكن استجابة الدعاء؟',
  //         ),
  //         _buildSuggestionChip(
  //           'قصص السيرة 📚',
  //           'حدثني عن قصص من السيرة النبوية حدثت في المدينة المنورة',
  //         ),
  //         _buildSuggestionChip(
  //           'مطاعم شعبية 🍖',
  //           'ما هي أشهر المطاعم الشعبية في المدينة المنورة؟',
  //         ),
  //         _buildSuggestionChip(
  //           'آداب الزيارة 🕌',
  //           'ما هي آداب زيارة المسجد النبوي الشريف؟',
  //         ),
  //         _buildSuggestionChip(
  //           'معالم إسلامية 🏛️',
  //           'ما هي أهم المعالم الإسلامية في المدينة؟',
  //         ),
  //         _buildSuggestionChip(
  //           'أماكن التسوق 🛍️',
  //           'أين أفضل أماكن التسوق والهدايا في المدينة المنورة؟',
  //         ),
  //         _buildSuggestionChip(
  //           'فضل المدينة ⭐',
  //           'حدثني عن فضل المدينة المنورة',
  //         ),
  //         _buildSuggestionChip(
  //           'الروضة الشريفة 🌸',
  //           'ما هي الروضة الشريفة وما فضلها؟',
  //         ),
  //       ],
  //     ),
  //   );
  // }
}
