import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiffada/screen/home/<USER>/__buildEvens.dart';
import 'package:wiffada/screen/home/<USER>/_buildChatButton.dart';
import 'package:wiffada/screen/home/<USER>/_buildVirtualToursSection.dart';
import 'package:wiffada/screen/home/<USER>/_buildSeerahSection.dart';
import 'package:wiffada/screen/home/<USER>/prayer_card_widget.dart';
import 'package:wiffada/screen/home/<USER>/prayer_times_bottom_sheet.dart';
import 'package:wiffada/screen/mosque_services/mosque_services_page.dart';
import 'package:wiffada/theme/app_theme.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:wiffada/utils/app_localizations.dart';
import 'package:wiffada/widgets/custom_snackbar.dart';
import 'package:provider/provider.dart';
import 'package:wiffada/providers/language_provider.dart';
import 'package:wiffada/providers/prayer_provider.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    initializeDateFormatting('ar'); // إضافة تهيئة التنسيق العربي

    // تهيئة مزود مواقيت الصلاة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      Provider.of<PrayerProvider>(context, listen: false).initialize();
    });

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  // تم نقل هذه الدوال إلى PrayerService و PrayerProvider

  @override
  Widget build(BuildContext context) {
    final colors = AppTheme.colors;
    final translations = AppLocalizations(
        Provider.of<LanguageProvider>(context).currentLanguage);

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        toolbarHeight: 80,
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF307371),
                Color(0xFF4F908E),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
            borderRadius: const BorderRadius.vertical(
              bottom: Radius.circular(25),
            ),
          ),
        ),
        title: Row(
          children: [
            Image.asset(
              'assets/images/wifada_logo.png',
              height: 40,
              width: 40,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  translations.translate('welcome_message'),
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
                Text(
                  translations.translate('app_subtitle'),
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 13,
                    color: Colors.white.withOpacity(0.9),
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          // زر الاشتراك
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(14),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.volunteer_activism_outlined,
                color: Colors.white,
                size: 22,
              ),
            ),
            onPressed: () {
              CustomSnackbar.show(
                context: context,
                message: AppLocalizations(
                        Provider.of<LanguageProvider>(context, listen: false)
                            .currentLanguage)
                    .translate('subscription_coming_soon'),
              );
            },
          ),
          // زر الإشعارات
          Container(
            margin: const EdgeInsets.only(left: 12),
            child: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 5,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.notifications_outlined,
                  color: Colors.white,
                  size: 22,
                ),
              ),
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      AppLocalizations(Provider.of<LanguageProvider>(context,
                                  listen: false)
                              .currentLanguage)
                          .translate('notifications_coming_soon'),
                      style: GoogleFonts.ibmPlexSansArabic(),
                      textAlign: TextAlign.center,
                    ),
                    duration: const Duration(seconds: 2),
                    behavior: SnackBarBehavior.floating,
                    backgroundColor: const Color(0xFF4F908E),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // خلفية متدرجة
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  colors.primary,
                  Colors.white.withOpacity(0.9),
                ],
                stops: const [0.0, 0.3],
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPrayerCard(),
                  const SizedBox(height: 20),
                  _buildMosqueAndMapSection(),
                  const SizedBox(height: 25),
                  const BuildChatButton(),
                  const SizedBox(height: 25),
                  const BuildSeerahSection(),
                  const SizedBox(height: 25),
                  const BuildVirtualToursSection(),
                  // const SizedBox(height: 25),
                  // const BuildEventsCard(),
                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrayerCard() {
    return PrayerCardWidget(
      onTap: () => _showPrayerTimesDialog(context),
    );
  }

  void _showPrayerTimesDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => const PrayerTimesBottomSheet(),
    );
  }

  // تم نقل هذه الدوال إلى PrayerService و PrayerProvider

  // دالة للانتقال إلى صفحة المسجد النبوي مع انيميشن بسيط
  void _navigateToMosqueWithAnimation(BuildContext context) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const MosqueServicesPage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // انيميشن تلاشي بسيط
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );
  }

  Widget _buildMosqueCard() {
    final decorations = AppTheme.decorations;
    final translations = AppLocalizations(
        Provider.of<LanguageProvider>(context).currentLanguage);

    return GestureDetector(
      onTap: () => _navigateToMosqueWithAnimation(context),
      child: Container(
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          image: const DecorationImage(
            image: AssetImage('assets/images/backgrounds/3.jpg'),
            fit: BoxFit.cover,
          ),
          boxShadow: decorations.cardShadow,
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withOpacity(0.7),
              ],
            ),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                translations.translate('prophets_mosque'),
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                translations.translate('explore_mosque_subtitle'),
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMapCard() {
    final decorations = AppTheme.decorations;
    final translations = AppLocalizations(
        Provider.of<LanguageProvider>(context).currentLanguage);

    return GestureDetector(
      onTap: () => Navigator.pushNamed(context, '/map'),
      child: Container(
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          image: const DecorationImage(
            image: AssetImage('assets/images/backgrounds/map.jpg'),
            fit: BoxFit.cover,
          ),
          boxShadow: decorations.cardShadow,
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color.fromARGB(255, 15, 15, 15).withOpacity(0.6),
                const Color.fromARGB(255, 20, 20, 20).withOpacity(0.9),
              ],
            ),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.map_outlined,
                  color: Color(0xFF4F908E),
                  size: 20,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                translations.translate('map'),
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                translations.translate('explore_map'),
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMosqueAndMapSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SizedBox(
        height: 200,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 3,
              child: _buildMosqueCard(),
            ),
            const SizedBox(width: 10),
            Expanded(
              flex: 2,
              child: _buildMapCard(),
            ),
          ],
        ),
      ),
    );
  }
}
