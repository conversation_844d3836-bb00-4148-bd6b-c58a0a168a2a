import 'dart:convert';
import 'dart:developer';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';

class ChatService {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _wsUrl = 'wss://api.openai.com/v1/realtime';
  
  // للمحادثة النصية
  static Future<String> sendTextMessage(String message) async {
    const url = "$_baseUrl/chat/completions";
    
    try {
      log("🟢 Sending text message to ChatGPT...");
      log("User Message: $message");

      final response = await http.post(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer ${dotenv.env['OPENAI_API_KEY']}",
        },
        body: jsonEncode({
          "model": "gpt-4o-mini",
          "messages": [
            {
              "role": "system",
              "content": """أنت وفادة، مساعدة افتراضية ودليل سياحي شامل لزوار المدينة المنورة. 

🌟 شخصيتك:
- مرحبة ولطيفة مع الزوار
- متخصصة في المدينة المنورة
- تقدم معلومات دقيقة ومفيدة
- تستخدم الرموز التعبيرية بشكل مناسب

📍 خدماتك تشمل:
- معلومات عن المسجد النبوي والروضة الشريفة
- آداب الزيارة والأحكام الشرعية
- المعالم الإسلامية والتاريخية
- المواصلات والخدمات
- المطاعم والفنادق
- نصائح عملية للزوار

🎯 أسلوبك:
- ابدأ بالسلام والترحيب في أول رد فقط
- كن مفيداً وعملياً
- اجعل إجاباتك واضحة ومنظمة
- استخدم الرموز التعبيرية لتجميل الرد"""
            },
            {"role": "user", "content": message},
          ],
          "max_tokens": 800,
          "temperature": 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final reply = data['choices'][0]['message']['content'];
        log("✅ ChatGPT Response: $reply");
        return reply;
      } else {
        log("❌ Error: ${response.statusCode} - ${response.body}");
        return "عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.";
      }
    } catch (e) {
      log("❌ Exception: $e");
      return "عذراً، حدث خطأ في الاتصال. يرجى التحقق من الإنترنت والمحاولة مرة أخرى.";
    }
  }

  // للمحادثة الصوتية
  static Future<WebSocketChannel?> connectVoiceChat() async {
    try {
      log("🎤 Connecting to OpenAI Realtime API...");
      
      final uri = Uri.parse(_wsUrl).replace(queryParameters: {
        'model': 'gpt-4o-realtime-preview-2024-12-17',
      });

      final channel = IOWebSocketChannel.connect(
        uri,
        headers: {
          'Authorization': 'Bearer ${dotenv.env['OPENAI_API_KEY']}',
          'OpenAI-Beta': 'realtime=v1',
        },
      );

      // إرسال إعدادات الجلسة
      final sessionConfig = {
        'type': 'session.update',
        'session': {
          'modalities': ['text', 'audio'],
          'instructions': """أنت وفادة، مساعدة صوتية ودليل سياحي للمدينة المنورة.

🎤 في المحادثة الصوتية:
- تحدثي بصوت واضح ومريح
- ابدئي بالسلام والترحيب في أول مرة فقط
- كوني مفيدة وودودة
- اجعلي إجاباتك مناسبة للاستماع (ليس طويلة جداً)

📍 تخصصك:
- تاريخ المدينة المنورة
- قصص الصحابة والسيرة النبوية
- فضائل المدينة وبركاتها
- المعالم المقدسة والمساجد
- نصائح عملية للزوار

🎯 أسلوبك الصوتي:
- صوت هادئ ومريح
- كلام واضح ومفهوم
- توقفات مناسبة
- نبرة ودودة ومرحبة""",
          'voice': 'alloy',
          'input_audio_format': 'pcm16',
          'output_audio_format': 'pcm16',
          'input_audio_transcription': {
            'model': 'whisper-1'
          },
          'turn_detection': {
            'type': 'server_vad',
            'threshold': 0.5,
            'prefix_padding_ms': 300,
            'silence_duration_ms': 200
          },
          'tools': [],
          'tool_choice': 'auto',
          'temperature': 0.8,
          'max_response_output_tokens': 4096
        }
      };

      channel.sink.add(jsonEncode(sessionConfig));
      log("✅ Voice chat session configured");
      
      return channel;
    } catch (e) {
      log("❌ Voice chat connection error: $e");
      return null;
    }
  }

  // إرسال رسالة صوتية
  static void sendVoiceMessage(WebSocketChannel channel, String message) {
    try {
      final messageData = {
        'type': 'conversation.item.create',
        'item': {
          'type': 'message',
          'role': 'user',
          'content': [
            {
              'type': 'input_text',
              'text': message
            }
          ]
        }
      };

      channel.sink.add(jsonEncode(messageData));

      // طلب الرد
      final responseRequest = {
        'type': 'response.create',
        'response': {
          'modalities': ['text', 'audio'],
          'instructions': 'أجيبي على سؤال المستخدم بشكل مفيد ومناسب للمحادثة الصوتية.'
        }
      };

      channel.sink.add(jsonEncode(responseRequest));
      log("🎤 Voice message sent: $message");
    } catch (e) {
      log("❌ Error sending voice message: $e");
    }
  }

  // إرسال صوت مباشر
  static void sendAudioData(WebSocketChannel channel, List<int> audioData) {
    try {
      final audioMessage = {
        'type': 'input_audio_buffer.append',
        'audio': base64Encode(audioData)
      };

      channel.sink.add(jsonEncode(audioMessage));
    } catch (e) {
      log("❌ Error sending audio data: $e");
    }
  }

  // إنهاء إدخال الصوت
  static void commitAudio(WebSocketChannel channel) {
    try {
      final commitMessage = {
        'type': 'input_audio_buffer.commit'
      };

      channel.sink.add(jsonEncode(commitMessage));
      
      // طلب الرد
      final responseRequest = {
        'type': 'response.create',
        'response': {
          'modalities': ['text', 'audio']
        }
      };

      channel.sink.add(jsonEncode(responseRequest));
      log("🎤 Audio committed and response requested");
    } catch (e) {
      log("❌ Error committing audio: $e");
    }
  }

  // إغلاق الاتصال
  static void closeVoiceChat(WebSocketChannel? channel) {
    try {
      channel?.sink.close();
      log("🔴 Voice chat connection closed");
    } catch (e) {
      log("❌ Error closing voice chat: $e");
    }
  }
}
