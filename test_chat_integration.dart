import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'lib/screen/ai_chat/ChatWithGPTPage.dart';
import 'lib/screen/ai_chat/enhanced_voice_chat.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تحميل متغيرات البيئة
  await dotenv.load(fileName: ".env");
  
  runApp(const ChatTestApp());
}

class ChatTestApp extends StatelessWidget {
  const ChatTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار المحادثة مع ChatGPT',
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'IBMPlexSansArabic',
      ),
      home: const ChatTestHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class ChatTestHomePage extends StatelessWidget {
  const ChatTestHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار المحادثة مع ChatGPT'),
        backgroundColor: const Color(0xFF4F908E),
        foregroundColor: Colors.white,
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF4F908E).withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة ChatGPT
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF4F908E).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.smart_toy,
                  size: 80,
                  color: Color(0xFF4F908E),
                ),
              ),
              
              const SizedBox(height: 30),
              
              const Text(
                '🤖 اختبار المحادثة مع ChatGPT',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 15),
              
              const Text(
                'تم ربط المحادثة بنجاح مع OpenAI API\nاختر نوع المحادثة للاختبار',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 50),
              
              // زر المحادثة النصية
              Container(
                width: 300,
                height: 60,
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ChatWithGPTPage(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.chat_bubble, size: 28),
                  label: const Text(
                    '💬 المحادثة النصية المحسنة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4F908E),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    elevation: 5,
                  ),
                ),
              ),
              
              // زر المحادثة الصوتية
              Container(
                width: 300,
                height: 60,
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const EnhancedVoiceChatScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.mic, size: 28),
                  label: const Text(
                    '🎤 المحادثة الصوتية المتقدمة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2E8B57),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    elevation: 5,
                  ),
                ),
              ),
              
              const SizedBox(height: 40),
              
              // معلومات الاختبار
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 30),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    const Text(
                      '✅ تم الربط بنجاح',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4F908E),
                      ),
                    ),
                    const SizedBox(height: 15),
                    const Text(
                      '• المحادثة النصية: أزرار سريعة + ChatGPT\n'
                      '• المحادثة الصوتية: WebSocket + Realtime API\n'
                      '• شخصية وفادة المخصصة للمدينة المنورة\n'
                      '• معالجة أخطاء محسنة ومراقبة الأداء\n'
                      '• تصميم متجاوب وتأثيرات بصرية جميلة',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 15),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                      ),
                      child: const Text(
                        '🟢 متصل مع OpenAI API',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
