import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';
import 'package:lottie/lottie.dart';
import 'dart:math' as math;

class EnhancedVoiceChatScreen extends StatefulWidget {
  const EnhancedVoiceChatScreen({super.key});

  @override
  State<EnhancedVoiceChatScreen> createState() =>
      _EnhancedVoiceChatScreenState();
}

class _EnhancedVoiceChatScreenState extends State<EnhancedVoiceChatScreen>
    with TickerProviderStateMixin {
  bool _isListening = false;
  final bool _isSpeaking = false;
  final bool _isConnected = false;
  String _status = 'اضغط للبدء';
  final String _currentMessage = '';
  final double _voiceLevel = 0.0;

  // Animation Controllers
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late AnimationController _breathingController;
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late AnimationController _rippleController;

  // Animations
  late Animation<double> _pulseAnimation;
  late Animation<double> _breathingAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Pulse animation for main button
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Wave animation for voice visualization
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Breathing animation for idle state
    _breathingController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    _breathingAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _breathingController, curve: Curves.easeInOut),
    );

    // Rotation animation for loading
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    // Scale animation for interactions
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );

    // Ripple animation for voice waves
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // Start breathing animation
    _breathingController.repeat(reverse: true);
    _waveController.repeat();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _breathingController.dispose();
    _rotationController.dispose();
    _scaleController.dispose();
    _rippleController.dispose();
    super.dispose();
  }

  void _toggleVoiceChat() async {
    if (_isListening) {
      _stopListening();
    } else {
      _startListening();
    }
  }

  void _startListening() {
    setState(() {
      _isListening = true;
      _status = 'أستمع إليك...';
    });
    _pulseController.repeat(reverse: true);
    _rippleController.repeat();
  }

  void _stopListening() {
    setState(() {
      _isListening = false;
      _status = 'اضغط للبدء';
    });
    _pulseController.stop();
    _rippleController.stop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(),
      body: Container(
        decoration: _buildBackgroundDecoration(),
        child: SafeArea(
          child: Column(
            children: [
              const SizedBox(height: 40),
              _buildStatusSection(),
              const SizedBox(height: 60),
              Expanded(
                child: _buildVoiceVisualization(),
              ),
              _buildControlsSection(),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Iconsax.arrow_left_2,
            color: Colors.white,
            size: 20,
          ),
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        'المحادثة الصوتية',
        style: GoogleFonts.ibmPlexSansArabic(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Iconsax.setting_2,
              color: Colors.white,
              size: 20,
            ),
          ),
          onPressed: () {
            // Settings
          },
        ),
      ],
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          const Color(0xFF4F908E),
          const Color(0xFF6BA3A0),
          const Color(0xFF4F908E).withValues(alpha: 0.8),
        ],
        stops: const [0.0, 0.5, 1.0],
      ),
    );
  }

  Widget _buildStatusSection() {
    return Column(
      children: [
        AnimatedBuilder(
          animation: _breathingAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _breathingAnimation.value,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _isListening ? Colors.green : Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _status,
                      style: GoogleFonts.ibmPlexSansArabic(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        if (_currentMessage.isNotEmpty) ...[
          const SizedBox(height: 20),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              _currentMessage,
              style: GoogleFonts.ibmPlexSansArabic(
                color: Colors.white,
                fontSize: 14,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildVoiceVisualization() {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer ripple effects
          if (_isListening) ..._buildRippleEffects(),

          // Voice wave visualization
          if (_isListening) _buildVoiceWaves(),

          // Main voice button
          _buildMainVoiceButton(),

          // Floating particles
          if (_isListening) ..._buildFloatingParticles(),
        ],
      ),
    );
  }

  List<Widget> _buildRippleEffects() {
    return List.generate(3, (index) {
      return AnimatedBuilder(
        animation: _rippleController,
        builder: (context, child) {
          final delay = index * 0.3;
          final animationValue = (_rippleController.value + delay) % 1.0;

          return Container(
            width: 200 + (animationValue * 100),
            height: 200 + (animationValue * 100),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white
                    .withValues(alpha: 0.3 - (animationValue * 0.3)),
                width: 2,
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildVoiceWaves() {
    return SizedBox(
      width: 300,
      height: 300,
      child: AnimatedBuilder(
        animation: _waveController,
        builder: (context, child) {
          return CustomPaint(
            painter: VoiceWavePainter(
              animationValue: _waveController.value,
              voiceLevel: _voiceLevel,
            ),
            size: const Size(300, 300),
          );
        },
      ),
    );
  }

  Widget _buildMainVoiceButton() {
    return AnimatedBuilder(
      animation: _isListening ? _pulseAnimation : _breathingAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale:
              _isListening ? _pulseAnimation.value : _breathingAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _scaleController.forward(),
            onTapUp: (_) => _scaleController.reverse(),
            onTapCancel: () => _scaleController.reverse(),
            onTap: _toggleVoiceChat,
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: _isListening
                            ? [
                                const Color(0xFF00E676),
                                const Color(0xFF00C853),
                              ]
                            : [
                                Colors.white,
                                Colors.white.withValues(alpha: 0.9),
                              ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: _isListening
                              ? const Color(0xFF00E676).withValues(alpha: 0.4)
                              : Colors.black.withValues(alpha: 0.2),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: Icon(
                      _isListening ? Iconsax.microphone_2 : Iconsax.microphone,
                      size: 50,
                      color:
                          _isListening ? Colors.white : const Color(0xFF4F908E),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildFloatingParticles() {
    return List.generate(8, (index) {
      return AnimatedBuilder(
        animation: _waveController,
        builder: (context, child) {
          final angle =
              (index * math.pi * 2 / 8) + (_waveController.value * math.pi * 2);
          final radius =
              80 + (math.sin(_waveController.value * math.pi * 2) * 20);
          final x = math.cos(angle) * radius;
          final y = math.sin(angle) * radius;

          return Transform.translate(
            offset: Offset(x, y),
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.6),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.3),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildControlsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildControlButton(
            icon: Iconsax.volume_high,
            label: 'مكبر الصوت',
            onTap: () {
              // Toggle speaker
            },
          ),
          _buildControlButton(
            icon: Iconsax.pause,
            label: 'إيقاف مؤقت',
            onTap: () {
              // Pause conversation
            },
          ),
          _buildControlButton(
            icon: Iconsax.call_slash,
            label: 'إنهاء',
            onTap: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.ibmPlexSansArabic(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

// Custom painter for voice wave visualization
class VoiceWavePainter extends CustomPainter {
  final double animationValue;
  final double voiceLevel;

  VoiceWavePainter({
    required this.animationValue,
    required this.voiceLevel,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.3)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final baseRadius = size.width / 4;

    // Draw multiple wave circles
    for (int i = 0; i < 5; i++) {
      final waveOffset = (animationValue + (i * 0.2)) % 1.0;
      final radius = baseRadius + (waveOffset * 50) + (voiceLevel * 20);
      final alpha = 0.5 - (waveOffset * 0.5);

      paint.color = Colors.white.withValues(alpha: alpha);
      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
